@page
@model _Cashdisbursment_.Pages.Admin.DashboardModel
@{
    ViewData["Title"] = "Admin Dashboard";
}

<div class="container py-2">
    <div class="page-header">
        <div class="row">
            <div class="col-12">
                <h1 class="h3 mb-1">
                    <i class="fas fa-tachometer-alt me-2"></i>Admin Dashboard
                </h1>
                <p class="text-muted mb-0">Zimdef Cash Disbursement System</p>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-3">
        <div class="col-md-3">
            <div class="card dashboard-card">
                <div class="card-body text-center">
                    <i class="fas fa-building fa-lg mb-2 text-primary"></i>
                    <h3>@Model.TotalCompanies</h3>
                    <p>Total Companies</p>
                    <small class="text-muted">@Model.ActiveCompanies Active</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card dashboard-card">
                <div class="card-body text-center">
                    <i class="fas fa-file-alt fa-lg mb-2 text-warning"></i>
                    <h3>@Model.TotalApplications</h3>
                    <p>Total Applications</p>
                    <small class="text-muted">@Model.PendingApplications Pending</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card dashboard-card">
                <div class="card-body text-center">
                    <i class="fas fa-check-circle fa-lg mb-2 text-success"></i>
                    <h3>@Model.ApprovedApplications</h3>
                    <p>Approved Applications</p>
                    <small class="text-muted">@Model.DisbursedApplications Disbursed</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card dashboard-card">
                <div class="card-body text-center">
                    <i class="fas fa-money-bill-wave fa-lg mb-2 text-info"></i>
                    <h3>$@Model.TotalDisbursed.ToString("N0")</h3>
                    <p>Total Disbursed</p>
                    <small class="text-muted">This Month: $@Model.MonthlyDisbursed.ToString("N0")</small>
                </div>
            </div>
        </div>
    </div>



    <div class="row">
        <!-- Pending Applications -->
        <div class="col-lg-6 mb-4">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><i class="fas fa-clock me-2"></i>Pending Applications</h5>
                    <a asp-page="/Admin/Applications" asp-route-filter="pending" class="btn btn-sm btn-outline-warning">View All</a>
                </div>
                <div class="card-body">
                    @if (Model.RecentPendingApplications.Any())
                    {
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>Company</th>
                                        <th>Amount</th>
                                        <th>Date</th>
                                        <th>Action</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var app in Model.RecentPendingApplications.Take(5))
                                    {
                                        <tr>
                                            <td>#@app.ApplicationID</td>
                                            <td>@app.Company?.Name</td>
                                            <td>$@app.RequestedCash.ToString("N0")</td>
                                            <td>@app.DateRequested.ToString("MMM dd")</td>
                                            <td>
                                                <a asp-page="/Admin/Applications/Review" asp-route-id="@app.ApplicationID" 
                                                   class="btn btn-sm btn-outline-primary">Review</a>
                                            </td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    }
                    else
                    {
                        <div class="text-center py-3">
                            <i class="fas fa-check-circle fa-2x text-success mb-2"></i>
                            <p class="text-muted mb-0">No pending applications</p>
                        </div>
                    }
                </div>
            </div>
        </div>

        <!-- Ready for Disbursement -->
        <div class="col-lg-6 mb-4">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><i class="fas fa-money-bill-wave me-2"></i>Ready for Disbursement</h5>
                    <a asp-page="/Admin/Applications" asp-route-filter="approved" class="btn btn-sm btn-outline-success">View All</a>
                </div>
                <div class="card-body">
                    @if (Model.ReadyForDisbursement.Any())
                    {
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>Company</th>
                                        <th>Amount</th>
                                        <th>Approved</th>
                                        <th>Action</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var app in Model.ReadyForDisbursement.Take(5))
                                    {
                                        <tr>
                                            <td>#@app.ApplicationID</td>
                                            <td>@app.Company?.Name</td>
                                            <td>$@app.RequestedCash.ToString("N0")</td>
                                            <td>@app.DateRequested.ToString("MMM dd")</td>
                                            <td>
                                                <a asp-page="/Admin/Applications/Disburse" asp-route-id="@app.ApplicationID" 
                                                   class="btn btn-sm btn-success">Disburse</a>
                                            </td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    }
                    else
                    {
                        <div class="text-center py-3">
                            <i class="fas fa-info-circle fa-2x text-info mb-2"></i>
                            <p class="text-muted mb-0">No applications ready for disbursement</p>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Activity -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-history me-2"></i>Recent Activity</h5>
                </div>
                <div class="card-body">
                    @if (Model.RecentApplications.Any())
                    {
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>Company</th>
                                        <th>Description</th>
                                        <th>Amount</th>
                                        <th>Date</th>
                                        <th>Status</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var app in Model.RecentApplications.Take(10))
                                    {
                                        <tr>
                                            <td><strong>#@app.ApplicationID</strong></td>
                                            <td>@app.Company?.Name</td>
                                            <td>
                                                <div class="text-truncate" style="max-width: 200px;" title="@app.Description">
                                                    @app.Description
                                                </div>
                                            </td>
                                            <td><strong>$@app.RequestedCash.ToString("N2")</strong></td>
                                            <td>@app.DateRequested.ToString("MMM dd, yyyy")</td>
                                            <td>
                                                @if (app.IsDisbursed)
                                                {
                                                    <span class="status-disbursed">Disbursed</span>
                                                }
                                                else if (app.Status == "Approved")
                                                {
                                                    <span class="status-approved">Approved</span>
                                                }
                                                else if (app.Status == "Rejected")
                                                {
                                                    <span class="status-rejected">Rejected</span>
                                                }
                                                else
                                                {
                                                    <span class="status-pending">Pending</span>
                                                }
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a asp-page="/Admin/Applications/Details" asp-route-id="@app.ApplicationID" 
                                                       class="btn btn-sm btn-outline-primary" title="View Details">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    @if (app.Status == "Pending" && !app.IsDisbursed)
                                                    {
                                                        <a asp-page="/Admin/Applications/Review" asp-route-id="@app.ApplicationID"
                                                           class="btn btn-sm btn-outline-warning" title="Review">
                                                            <i class="fas fa-gavel"></i>
                                                        </a>
                                                    }
                                                    @if (app.Status == "Approved" && !app.IsDisbursed)
                                                    {
                                                        <a asp-page="/Admin/Applications/Disburse" asp-route-id="@app.ApplicationID" 
                                                           class="btn btn-sm btn-outline-success" title="Disburse">
                                                            <i class="fas fa-money-bill-wave"></i>
                                                        </a>
                                                    }
                                                </div>
                                            </td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    }
                    else
                    {
                        <div class="text-center py-4">
                            <i class="fas fa-file-alt fa-3x text-muted mb-3"></i>
                            <p class="text-muted">No recent applications</p>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>
