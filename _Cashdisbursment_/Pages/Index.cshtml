﻿@page
@model IndexModel
@{
    ViewData["Title"] = "Home";
}

<div class="hero-section">
    <div class="container">
        <div class="row align-items-center max-vh-75 ">
            <div class="col-lg-6">
                <div class="hero-content">
                    <h1 class="display-5 mb-4">Zimdef Fund Management</h1>
                    <p class="lead mb-4">Streamline fund applications, approvals, and disbursements with our comprehensive management platform designed for efficiency and transparency.</p>
                    <div class="d-flex flex-column flex-sm-row gap-3">
                        @if (Model.IsAuthenticated)
                        {
                            @if (Model.IsAdmin)
                            {
                                <a asp-page="/Admin/Dashboard" class="btn btn-light btn-lg">
                                    Access Admin Portal
                                </a>
                            }
                            else
                            {
                                <a asp-page="/Company/Dashboard" class="btn btn-light btn-lg">
                                    Go to Dashboard
                                </a>
                            }
                        }
                        else
                        {
                            <a asp-page="/Account/Login" class="btn btn-light btn-lg">
                                Sign In
                            </a>
                            <a asp-page="/Account/Register" class="btn btn-outline-light btn-lg">
                                Register Company
                            </a>
                        }
                    </div>
                </div>
            </div>
            <div class="col-lg-6">
                <div class="hero-visual text-center">
                    <div class="hero-icon-container">
                        <i class="fas fa-university"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>


</div>
