@using _Cashdisbursment_.Utilities
@{
    var currentUser = AuthUtility.GetCurrentUser(Context.Session);
    var isAuthenticated = currentUser != null;
    var isAdmin = AuthUtility.IsAdmin(Context.Session);
    var isCompany = AuthUtility.IsCompanyUser(Context.Session);
    var isApprover = AuthUtility.IsApprover(Context.Session);
    var hasApprovalAccess = AuthUtility.HasApprovalAccess(Context.Session);
}

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>@ViewData["Title"] - Zimdef Cash Disbursement System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="~/css/custom.css" asp-append-version="true" />
</head>
<body>
    <header>
        <nav class="navbar navbar-expand-lg">
            <div class="container">
                <a class="navbar-brand" asp-page="/Index">
                    <i class="fas fa-university me-2"></i>Zimdef
                </a>
                <button class="navbar-toggler border-0" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                    <span class="navbar-toggler-icon"></span>
                </button>
                <div class="collapse navbar-collapse" id="navbarNav">
                    <ul class="navbar-nav me-auto">
                        @if (isAuthenticated)
                        {
                            @if (isAdmin)
                            {
                                <li class="nav-item">
                                    <a class="nav-link" asp-page="/Admin/Dashboard">Dashboard</a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" asp-page="/Admin/Companies">Companies</a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" asp-page="/Admin/Applications">Applications</a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" asp-page="/Admin/Acquittals">Acquittals</a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" asp-page="/Admin/Approvers">Approvers</a>
                                </li>
                             
                             
                            }
                            else if (isApprover)
                            {
                                <li class="nav-item">
                                    <a class="nav-link" asp-page="/Approver/Dashboard">Dashboard</a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" asp-page="/Approver/Applications">Applications</a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" asp-page="/Admin/Companies" class="text-muted">Companies (Read-Only)</a>
                                </li>
                            }
                            else if (isCompany)
                            {
                                <li class="nav-item">
                                    <a class="nav-link" asp-page="/Company/Dashboard">Dashboard</a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" asp-page="/Company/Applications">Applications</a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" asp-page="/Company/Acquittals">Acquittals</a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" asp-page="/Company/Profile">Profile</a>
                                </li>
                                <!-- Company Quick Actions -->
                                <li class="nav-item dropdown">
                                    <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                                        <i class="fas fa-bolt me-1"></i>Quick Actions
                                    </a>
                                    <ul class="dropdown-menu">
                                        <li><a class="dropdown-item" asp-page="/Company/Applications/Create">
                                            <i class="fas fa-plus me-2"></i>New Application
                                        </a></li>
                                        <li><a class="dropdown-item" asp-page="/Company/Applications">
                                            <i class="fas fa-list me-2"></i>View Applications
                                        </a></li>
                                        <li><a class="dropdown-item" asp-page="/Company/Profile">
                                            <i class="fas fa-user me-2"></i>Update Profile
                                        </a></li>
                                    </ul>
                                </li>
                            }
                        }
                    </ul>
                    <ul class="navbar-nav">
                        @if (isAuthenticated)
                        {
                            <li class="nav-item dropdown">
                                <a class="nav-link dropdown-toggle d-flex align-items-center" href="#" role="button" data-bs-toggle="dropdown">
                                    <div class="d-flex align-items-center">
                                        <div class="avatar-circle me-2">
                                            @currentUser?.Email.Substring(0, 1).ToUpper()
                                        </div>
                                        <span class="d-none d-md-inline">@currentUser?.Email</span>
                                    </div>
                                </a>
                                <ul class="dropdown-menu dropdown-menu-end">
                                    <li><h6 class="dropdown-header">Account</h6></li>
                                    <li><a class="dropdown-item" asp-page="/Account/ChangePassword">Change Password</a></li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li><a class="dropdown-item text-danger" asp-page="/Account/Logout">Sign Out</a></li>
                                </ul>
                            </li>
                        }
                        else
                        {
                            <li class="nav-item">
                                <a class="nav-link" asp-page="/Account/Login">Sign In</a>
                            </li>
                            <li class="nav-item">
                                <a class="btn btn-primary ms-2" asp-page="/Account/Register">Get Started</a>
                            </li>
                        }
                    </ul>
                </div>
            </div>
        </nav>
    </header>

    <main role="main" class="main-content">
        <div class="content-wrapper">
            @RenderBody()
        </div>
    </main>

    <footer class="footer">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <p class="mb-0">&copy; 2025 Zimdef. All rights reserved.</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <small class="text-muted">Cash Disbursement System</small>
                </div>
            </div>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="~/js/site.js" asp-append-version="true"></script>

    @await RenderSectionAsync("Scripts", required: false)
</body>
</html>
