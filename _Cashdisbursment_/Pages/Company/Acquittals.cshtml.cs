using _Cashdisbursment_.Models;
using _Cashdisbursment_.Services;
using _Cashdisbursment_.Utilities;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;

namespace _Cashdisbursment_.Pages.Company
{
    public class AcquittalsModel : PageModel
    {
        private readonly AcquittalService _acquittalService;

        public AcquittalsModel(AcquittalService acquittalService)
        {
            _acquittalService = acquittalService;
        }

        public User? CurrentUser { get; set; }
        public List<Acquittal> Acquittals { get; set; } = new();
        public List<AcquittalSubmission> AcquittalSubmissions { get; set; } = new();
        public double TotalDisbursed { get; set; }
        public double TotalAcquitted { get; set; }

        [TempData]
        public string? SuccessMessage { get; set; }

        [TempData]
        public string? ErrorMessage { get; set; }

        public async Task<IActionResult> OnGetAsync()
        {
            CurrentUser = AuthUtility.GetCurrentUser(HttpContext.Session);

            if (CurrentUser == null || !AuthUtility.IsCompanyUser(HttpContext.Session))
            {
                return RedirectToPage("/Account/Login");
            }

            try
            {
                // Get all acquittals for the company's disbursed applications
                Acquittals = await _acquittalService.GetAcquittalsByCompanyAsync(CurrentUser.CompanyID!.Value);

                // Get all acquittal submissions for these acquittals
                foreach (var acquittal in Acquittals)
                {
                    var submissions = await _acquittalService.GetSubmissionsByAcquittalIdAsync(acquittal.AcquittalID);
                    AcquittalSubmissions.AddRange(submissions);
                }

                // Calculate totals
                TotalDisbursed = Acquittals.Sum(a => a.Application?.DisbursedCash ?? 0);
                TotalAcquitted = AcquittalSubmissions.Sum(s => s.Amount);

                return Page();
            }
            catch (Exception ex)
            {
                ErrorMessage = "An error occurred while loading acquittals. Please try again.";
                return Page();
            }
        }
    }
}
